image: node:latest

variables:
    GIT_DEPTH: 10
#     CocosCreator: '\ProgramData\cocos\editors\Creator\3.8.4\CocosCreator.exe'
# S3 配置 (需要在GitLab CI/CD设置中配置以下变量)
# S3_ACCESS_KEY_ID: S3的访问密钥ID
# S3_SECRET_ACCESS_KEY: S3的秘密访问密钥
# S3_ENDPOINT: S3的终端节点URL，例如 https://wh.s3down.com
# S3_REGION: S3的区域，例如 wh
# S3_BUCKET: S3的存储桶名称（联调、测试、预发布环境共用）
# S3_BUCKET_PRODUCTION: S3的线上环境存储桶名称
# 通知服务端配置 (需要在GitLab CI/CD设置中配置以下变量)
# NOTIFY_API_URL_DEV: 联调环境通知服务端的API URL
# NOTIFY_API_URL_TEST: 测试环境通知服务端的API URL
# NOTIFY_API_URL_PRE: 预发布环境通知服务端的API URL
# NOTIFY_API_URL_PROD: 线上环境通知服务端的API URL
# NOTIFY_API_TOKEN: 通知服务端的API Token (可选)

# 构建步骤
stages:
    - docs
    # 依赖安装
    - install

    # 运行测试
    # - test

    # 代码构建
    - build

    # 生成语音房需要的zip包
    - build_zip

    # 上传构建包到S3 （各环境）
    - upload_s3_dev # 联调环境
    - upload_s3_test # 测试环境
    - upload_s3_pre # 预发布环境
    - upload_s3_prod # 线上环境

    # 部署联调环境
    - deployAuto

    # 部署测试环境
    - deployTesting

    # 部署预发布环境
    #  - deployPre

    # 项目发布上线
    - release

# 同步文档到文档站
deploy-docs:
    stage: docs
    script:
        - rsync -av ./docs/* /webser/www/suileyoo-hexo/source/oapi/partygame/cocos/
        - cd /webser/www/suileyoo-hexo/
        - npm run gen
    tags: [suileyoo-deploy]
    when: manual

# 安装依赖
install_dependency:
    stage: install
    # 把文件传递到下一个job expire_in 过期时间 建议加上
    # artifacts:
    #   # 把没版本跟踪的产物发送到 下一个任务
    #   untracked: true
    #   expire_in: 1 week
    before_script:
        - whoami
        - pwd
        - source ~/.nvm/nvm.sh
        - echo ${PATH}
        - cat ~/.bashrc
        - nvm ls
        - node -v
    script:
        # - nvm use 16
        #    # 开始安装
        # - echo "begin nvm use"
        # - nvm use 20.16.0

        # - nvm ls
        # - nvm use 22.11.0
        # - node -v

        # - yarn config set cache-folder /mnt/d/YarnCache
        - echo "begin installing..."
        #    # 安装模块依赖：包含deps和devDeps
        #    # - npm config set cache /cache/npm-cache --global
        - yarn install
    cache:
        key: ${CI_COMMIT_REF_NAME}_${CI_PROJECT_ID}
        paths: # 缓存路径
            - node_modules/
        policy: push
    # only:
    #   - develop
    #   - testing
    #   - release
    #   - master
    only:
        changes:
            - .gitlab-ci.yml
            - package.json
            - yarn-lock.json

    tags:
        - liucl-macos

.job_build_bundles:
    script:
        # - mkdir -p packages/game-shark/node_modules && cp node_modules/sgc packages/game-shark/node_modules/ -r &&  cp node_modules/pitayaclient packages/game-shark/node_modules/ -r
        # - yarn workspace game-shark run build
        # - /mnt/d/ProgramData/cocos/editors/Creator/3.8.4/CocosCreator.exe --project "./packages/game-shark/" --build "configPath=./packages/game-shark/buildConfig_web-mobile.json"
        - /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/MacOS/CocosCreator  --project "${DIR}" --build "configPath=${DIR}buildConfig_web-mobile.json" 2>&1 | tee output.log || true
    cache:
        key: ${CI_COMMIT_REF_NAME}_${CI_PROJECT_ID}
        paths: # 缓存路径
            - node_modules/
        policy: pull # 禁止上传更新缓存
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}'
        paths:
            - '${BUILD_PATH}'
    tags:
        - liucl-macos

# 鲨鱼游戏构建
job_build_shark:
    stage: build
    variables:
        DIR: './packages/game-shark/'
        BUILD_PATH: 'packages/game-shark/build'
    extends:
        - .job_build_bundles # 继承构建项目脚本
    only:
        changes:
            - .gitlab-ci.yml
            - yarn.lock
            - package-lock.json
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏构建
job_build_pirate:
    stage: build
    variables:
        DIR: './packages/game-pirate/'
        BUILD_PATH: 'packages/game-pirate/build'
    extends:
        - .job_build_bundles # 继承构建项目脚本
    only:
        changes:
            - .gitlab-ci.yml
            - yarn.lock
            - package-lock.json
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏构建
job_build_game_dixit:
    stage: build
    variables:
        DIR: './packages/game-dixit/'
        BUILD_PATH: 'packages/game-dixit/build'
    extends:
        - .job_build_bundles # 继承构建项目脚本
    only:
        changes:
            - .gitlab-ci.yml
            - yarn.lock
            - package-lock.json
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏构建
job_build_flyingchess:
    stage: build
    variables:
        DIR: './packages/game-flyingchess/'
        BUILD_PATH: 'packages/game-flyingchess/build'
    extends:
        - .job_build_bundles # 继承构建项目脚本
    only:
        changes:
            - .gitlab-ci.yml
            - yarn.lock
            - package-lock.json
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

.job_build_zip:
    script:
        - ls
        - cp -r ${BUILD_PATH}/* ./

    stage: build_zip
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}_zip'
        paths:
            - web-mobile/*
    tags:
        - liucl-macos

build_zip_shark:
    variables:
        BUILD_PATH: 'packages/game-shark/build'
    dependencies:
        - job_build_shark
    extends:
        - .job_build_zip
    only:
        changes:
            - .gitlab-ci.yml
            - package-lock.json
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

build_zip_pirate:
    variables:
        BUILD_PATH: 'packages/game-pirate/build'
    dependencies:
        - job_build_pirate
    extends:
        - .job_build_zip
    only:
        changes:
            - .gitlab-ci.yml
            - package-lock.json
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

build_zip_game_dixit:
    variables:
        BUILD_PATH: 'packages/game-dixit/build'
    dependencies:
        - job_build_game_dixit
    extends:
        - .job_build_zip
    only:
        changes:
            - .gitlab-ci.yml
            - package-lock.json
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

build_zip_flyingchess:
    variables:
        BUILD_PATH: 'packages/game-flyingchess/build'
    dependencies:
        - job_build_flyingchess
    extends:
        - .job_build_zip
    only:
        changes:
            - .gitlab-ci.yml
            - package-lock.json
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# S3上传和通知服务端的基础模板（通用部分）
.job_upload_s3_base:
    variables:
        S3_REGION: ${S3_REGION}
        S3_ACCESS_KEY_ID: ${S3_ACCESS_KEY_ID}
        S3_SECRET_ACCESS_KEY: ${S3_SECRET_ACCESS_KEY}
        S3_ENDPOINT: ${S3_ENDPOINT}
        NOTIFY_API_TOKEN: ${NOTIFY_API_TOKEN}
    before_script:
        - source ~/.nvm/nvm.sh
    script:
        - echo "开始打包构建文件..."
        - zip -r ./${GAME_CODE}-${CI_COMMIT_SHORT_SHA}.zip ./web-mobile/
        - echo "开始上传构建包到S3..."
        - node scripts/upload-to-s3.js ${GAME_CODE} ${CI_COMMIT_SHORT_SHA}
        - echo "开始通知服务端..."
        - node scripts/notify-server.js
    cache:
        key: ${CI_COMMIT_REF_NAME}_${CI_PROJECT_ID}
        paths:
            - node_modules/
        policy: pull
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}_s3_upload'
        paths:
            - upload-result.json
            - '*.zip'
    tags:
        - liucl-macos
    # 需要手动触发
    when: manual

# 联调环境上传S3基础模板
.job_upload_s3_dev:
    stage: upload_s3_dev
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_DEV}
    extends:
        - .job_upload_s3_base

# 测试环境上传S3基础模板
.job_upload_s3_test:
    stage: upload_s3_test
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_TEST}
    extends:
        - .job_upload_s3_base

# 预发布环境上传S3基础模板
.job_upload_s3_pre:
    stage: upload_s3_pre
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_PRE}
    extends:
        - .job_upload_s3_base

# 线上环境上传S3基础模板
.job_upload_s3_prod:
    stage: upload_s3_prod
    variables:
        S3_BUCKET: ${S3_BUCKET_PRODUCTION}
        NOTIFY_API_URL: ${NOTIFY_API_URL_PROD}
    extends:
        - .job_upload_s3_base

# 鲨鱼游戏上传S3 - 联调环境
upload_s3_dev_shark:
    variables:
        GAME_CODE: 'care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_upload_s3_dev
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 鲨鱼游戏上传S3 - 测试环境
upload_s3_test_shark:
    variables:
        GAME_CODE: 'care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_upload_s3_test
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 鲨鱼游戏上传S3 - 预发布环境
upload_s3_pre_shark:
    variables:
        GAME_CODE: 'care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_upload_s3_pre
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 鲨鱼游戏上传S3 - 线上环境
upload_s3_prod_shark:
    variables:
        GAME_CODE: 'care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_upload_s3_prod
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏上传S3 - 联调环境
upload_s3_dev_pirate:
    variables:
        GAME_CODE: 'pirate'
    dependencies:
        - build_zip_pirate
    extends:
        - .job_upload_s3_dev
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏上传S3 - 测试环境
upload_s3_test_pirate:
    variables:
        GAME_CODE: 'pirate'
    dependencies:
        - build_zip_pirate
    extends:
        - .job_upload_s3_test
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏上传S3 - 预发布环境
upload_s3_pre_pirate:
    variables:
        GAME_CODE: 'pirate'
    dependencies:
        - build_zip_pirate
    extends:
        - .job_upload_s3_pre
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏上传S3 - 线上环境
upload_s3_prod_pirate:
    variables:
        GAME_CODE: 'pirate'
    dependencies:
        - build_zip_pirate
    extends:
        - .job_upload_s3_prod
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏上传S3 - 联调环境
upload_s3_dev_dixit:
    variables:
        GAME_CODE: 'dixit'
    dependencies:
        - build_zip_game_dixit
    extends:
        - .job_upload_s3_dev
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏上传S3 - 联调环境
upload_s3_dev_flyingchess:
    variables:
        GAME_CODE: 'flyingchess'
    dependencies:
        - build_zip_flyingchess
    extends:
        - .job_upload_s3_dev
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏上传S3 - 测试环境
upload_s3_test_dixit:
    variables:
        GAME_CODE: 'dixit'
    dependencies:
        - build_zip_game_dixit
    extends:
        - .job_upload_s3_test
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏上传S3 - 测试环境
upload_s3_test_flyingchess:
    variables:
        GAME_CODE: 'flyingchess'
    dependencies:
        - build_zip_flyingchess
    extends:
        - .job_upload_s3_test
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏上传S3 - 预发布环境
upload_s3_pre_dixit:
    variables:
        GAME_CODE: 'dixit'
    dependencies:
        - build_zip_game_dixit
    extends:
        - .job_upload_s3_pre
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏上传S3 - 预发布环境
upload_s3_pre_flyingchess:
    variables:
        GAME_CODE: 'flyingchess'
    dependencies:
        - build_zip_flyingchess
    extends:
        - .job_upload_s3_pre
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏上传S3 - 线上环境
upload_s3_prod_dixit:
    variables:
        GAME_CODE: 'dixit'
    dependencies:
        - build_zip_game_dixit
    extends:
        - .job_upload_s3_prod
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏上传S3 - 线上环境
upload_s3_prod_flyingchess:
    variables:
        GAME_CODE: 'flyingchess'
    dependencies:
        - build_zip_flyingchess
    extends:
        - .job_upload_s3_prod
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

.job_deploy_dev:
    # only:
    #   - develop
    environment:
        name: auto
    cache: {}
    script:
        # 开始部署
        - echo "deploying..."
        - cd $DIR
        # rsync部署
        - rsync -av ./ --exclude=.git --exclude=.env  $DEPLOY_DEST --delete
    # 需要手动部署
    when: manual

# 联调环境部署-鲨鱼
deploy_dev_shark:
    stage: deployAuto
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_shark
    variables:
        DIR: 'packages/game-shark/build'
        DEPLOY_DEST: '/webser/www/cocos-games-dev/shark'
    tags:
        - suileyoo-deploy-testing

# 联调环境部署-海盗桶
deploy_dev_pirate:
    stage: deployAuto
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_pirate
    variables:
        DIR: 'packages/game-pirate/build'
        DEPLOY_DEST: '/webser/www/cocos-games-dev/game-pirate'
    tags:
        - suileyoo-deploy-testing

# 联调环境部署-只言片语
deploy_dev_game_dixit:
    stage: deployAuto
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_game_dixit
    variables:
        DIR: 'packages/game-dixit/build'
        DEPLOY_DEST: '/webser/www/cocos-games-dev/game-dixit'
    tags:
        - suileyoo-deploy-testing

# 联调环境部署-飞行棋
deploy_dev_flyingchess:
    stage: deployAuto
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_flyingchess
    variables:
        DIR: 'packages/game-flyingchess/build'
        DEPLOY_DEST: '/webser/www/cocos-games-dev/game-flyingchess'
    tags:
        - suileyoo-deploy-testing

# 测试环境部署-鲨鱼
deploy_test_shark:
    stage: deployTesting
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_shark
    variables:
        DIR: 'packages/game-shark/build'
        DEPLOY_DEST: '/webser/www/cocos-games/shark'
    tags:
        - suileyoo-deploy-testing

# 测试环境部署-海盗桶
deploy_test_pirate:
    stage: deployTesting
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_pirate
    variables:
        DIR: 'packages/game-pirate/build'
        DEPLOY_DEST: '/webser/www/cocos-games/game-pirate'
    tags:
        - suileyoo-deploy-testing

# 测试环境部署-只言片语
deploy_test_game_dixit:
    stage: deployTesting
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_game_dixit
    variables:
        DIR: 'packages/game-dixit/build'
        DEPLOY_DEST: '/webser/www/cocos-games/game-dixit'
    tags:
        - suileyoo-deploy-testing

# 测试环境部署-飞行棋
deploy_test_flyingchess:
    stage: deployTesting
    extends:
        - .job_deploy_dev # 继承
    dependencies:
        - job_build_flyingchess
    variables:
        DIR: 'packages/game-flyingchess/build'
        DEPLOY_DEST: '/webser/www/cocos-games/game-flyingchess'
    tags:
        - suileyoo-deploy-testing

# 方舟平台发布公共配置
.job_release:
    stage: release
    environment:
        name: production
    variables:
        # 方舟平台提供的api接口
        ark_api: 'https://ark.shengtian.com/api/cicd/workflow/gitlab_job'
    script:
        # 如果有CI环境变量的话可以自行配置CI环境变量，否则就使用默认值
        - echo "request params :" "{\"project_id\":${CI_PROJECT_ID},\"module_id\":${module_id},\"job_id\":\"${CI_JOB_ID}\"}"
        - res=$(eval $(echo "curl -v -H 'Content-type:application/json' -X POST -d '{\"project_id\":${CI_PROJECT_ID},\"module_id\":${module_id},\"job_id\":${CI_JOB_ID}}' ${ark_api}"))
        - echo "response:${res}"
        - if [[ ${res}  =~ "200" ]]; then echo "success"; else echo "fail"; exit 1; fi
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}'
        paths:
            - .${build_dir}
    when: manual
    only:
        - master
    tags:
        - pan_ci

# 鲨鱼游戏发布
release_shark:
    extends:
        - .job_release
    dependencies:
        - job_build_shark
    variables:
        build_dir: '/packages/game-shark/build'
        module_id: '$ARK_MODULE_ID_SHARK'
    only:
        changes:
            - .gitlab-ci.yml
            - packages/game-shark/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 海盗桶游戏发布
release_pirate:
    extends:
        - .job_release
    dependencies:
        - job_build_pirate
    variables:
        build_dir: '/packages/game-pirate/build'
        module_id: '$ARK_MODULE_ID_PIRATE'
    only:
        changes:
            - .gitlab-ci.yml
            - packages/game-pirate/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 只言片语游戏发布
release_game_dixit:
    extends:
        - .job_release
    dependencies:
        - job_build_game_dixit
    variables:
        build_dir: '/packages/game-dixit/build'
        module_id: '$ARK_MODULE_ID_DIXIT'
    only:
        changes:
            - .gitlab-ci.yml
            - packages/game-dixit/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*

# 飞行棋游戏发布
release_flyingchess:
    extends:
        - .job_release
    dependencies:
        - job_build_flyingchess
    variables:
        build_dir: '/packages/game-flyingchess/build'
        module_id: '$ARK_MODULE_ID_FLYINGCHESS'
    only:
        changes:
            - .gitlab-ci.yml
            - packages/game-flyingchess/**/*
            - packages/pitayaclient/**/*
            - packages/sgc/**/*
